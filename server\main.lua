local ESX = nil

TriggerEvent('esx:getSharedObject', function(obj) ESX = obj end)

-- Get inventory data callback
ESX.RegisterServerCallback('inventory_system:getData', function(source, cb)
    local xPlayer = ESX.GetPlayerFromId(source)
    if not xPlayer then
        cb(nil)
        return
    end

    local inventory = xPlayer.getInventory()
    local weight = 0.0
    
    -- Calculate total weight
    for i = 1, #inventory do
        if inventory[i].count > 0 then
            weight = weight + (inventory[i].weight * inventory[i].count)
        end
    end

    local data = {
        inventory = inventory,
        vendor = Config.VendorStock,
        weight = weight,
        maxWeight = xPlayer.maxWeight or 25.0
    }

    cb(data)
end)

-- Use item callback
ESX.RegisterServerCallback('inventory_system:useItem', function(source, cb, itemName)
    local xPlayer = ESX.GetPlayerFromId(source)
    if not xPlayer then
        cb(false)
        return
    end

    local item = xPlayer.getInventoryItem(itemName)
    if item and item.count > 0 then
        xPlayer.removeInventoryItem(itemName, 1)
        
        -- Add item-specific effects here
        if itemName == 'bread' then
            TriggerClientEvent('esx_status:add', source, 'hunger', 200000)
        elseif itemName == 'water' then
            TriggerClientEvent('esx_status:add', source, 'thirst', 200000)
        elseif itemName == 'bandage' then
            TriggerClientEvent('esx_basicneeds:healPlayer', source)
        end
        
        cb(true)
    else
        cb(false)
    end
end)

-- Buy item callback
ESX.RegisterServerCallback('inventory_system:buyItem', function(source, cb, itemName, quantity)
    local xPlayer = ESX.GetPlayerFromId(source)
    if not xPlayer then
        cb(false)
        return
    end

    quantity = quantity or 1
    local vendorItem = nil
    
    -- Find item in vendor stock
    for i = 1, #Config.VendorStock do
        if Config.VendorStock[i].name == itemName then
            vendorItem = Config.VendorStock[i]
            break
        end
    end

    if not vendorItem then
        cb(false)
        return
    end

    local totalPrice = vendorItem.price * quantity
    local playerMoney = xPlayer.getMoney()

    if playerMoney >= totalPrice then
        local item = xPlayer.getInventoryItem(itemName)
        if item and (item.count + quantity) <= vendorItem.max then
            xPlayer.removeMoney(totalPrice)
            xPlayer.addInventoryItem(itemName, quantity)
            cb(true)
        else
            cb(false)
        end
    else
        cb(false)
    end
end)

-- Sell item callback
ESX.RegisterServerCallback('inventory_system:sellItem', function(source, cb, itemName, quantity)
    local xPlayer = ESX.GetPlayerFromId(source)
    if not xPlayer then
        cb(false)
        return
    end

    quantity = quantity or 1
    local vendorItem = nil
    
    -- Find item in vendor stock
    for i = 1, #Config.VendorStock do
        if Config.VendorStock[i].name == itemName then
            vendorItem = Config.VendorStock[i]
            break
        end
    end

    if not vendorItem then
        cb(false)
        return
    end

    local item = xPlayer.getInventoryItem(itemName)
    if item and item.count >= quantity then
        local sellPrice = math.floor(vendorItem.price * 0.7 * quantity) -- 70% of buy price
        xPlayer.removeInventoryItem(itemName, quantity)
        xPlayer.addMoney(sellPrice)
        cb(true)
    else
        cb(false)
    end
end)
