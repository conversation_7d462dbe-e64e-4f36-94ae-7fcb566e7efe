* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Arial', sans-serif;
    background: transparent;
    overflow: hidden;
}

.hidden {
    display: none !important;
}

#inventory-container {
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 900px;
    height: 600px;
    background: rgba(0, 0, 0, 0.9);
    border: 2px solid #333;
    border-radius: 10px;
    color: white;
    backdrop-filter: blur(5px);
}

.inventory-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px 20px;
    border-bottom: 1px solid #333;
    background: rgba(0, 0, 0, 0.5);
}

.weight-info {
    display: flex;
    align-items: center;
    gap: 10px;
}

#weight-text {
    font-size: 14px;
    font-weight: bold;
    min-width: 100px;
}

.weight-bar {
    width: 200px;
    height: 8px;
    background: #333;
    border-radius: 4px;
    overflow: hidden;
}

.weight-fill {
    height: 100%;
    background: linear-gradient(90deg, #4CAF50, #FFC107, #F44336);
    transition: width 0.3s ease;
    width: 0%;
}

.close-btn {
    background: #F44336;
    color: white;
    border: none;
    width: 30px;
    height: 30px;
    border-radius: 50%;
    font-size: 18px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
}

.close-btn:hover {
    background: #D32F2F;
}

.inventory-content {
    display: flex;
    height: calc(100% - 70px);
}

.inventory-panel, .vendor-panel {
    flex: 1;
    padding: 20px;
    border-right: 1px solid #333;
}

.vendor-panel {
    border-right: none;
}

.inventory-panel h3, .vendor-panel h3 {
    margin-bottom: 15px;
    text-align: center;
    color: #FFC107;
    font-size: 18px;
}

.item-grid {
    display: grid;
    grid-template-columns: repeat(6, 1fr);
    grid-template-rows: repeat(6, 1fr);
    gap: 5px;
    height: calc(100% - 40px);
}

.item-slot {
    background: rgba(255, 255, 255, 0.1);
    border: 2px solid #333;
    border-radius: 8px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.2s ease;
    position: relative;
    min-height: 70px;
}

.item-slot:hover {
    border-color: #FFC107;
    background: rgba(255, 193, 7, 0.1);
}

.item-slot.success {
    border-color: #4CAF50;
    background: rgba(76, 175, 80, 0.2);
}

.item-slot.error {
    border-color: #F44336;
    background: rgba(244, 67, 54, 0.2);
}

.item-icon {
    width: 40px;
    height: 40px;
    object-fit: contain;
    margin-bottom: 5px;
}

.item-count {
    font-size: 12px;
    font-weight: bold;
    color: #FFC107;
}

.item-name {
    font-size: 10px;
    color: #ccc;
    text-align: center;
    margin-top: 2px;
}

.item-price {
    font-size: 10px;
    color: #4CAF50;
    text-align: center;
    margin-top: 2px;
}

.empty-slot {
    opacity: 0.3;
}

.empty-slot:hover {
    opacity: 0.5;
}
