<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Inventory System</title>
    <link rel="stylesheet" href="css/app.css">
</head>
<body>
    <div id="inventory-container" class="hidden">
        <div class="inventory-header">
            <div class="weight-info">
                <span id="weight-text">0.0 / 25.0 KG</span>
                <div class="weight-bar">
                    <div id="weight-fill" class="weight-fill"></div>
                </div>
            </div>
            <button id="close-btn" class="close-btn">×</button>
        </div>
        
        <div class="inventory-content">
            <div class="inventory-panel">
                <h3>Inventory</h3>
                <div id="inventory-grid" class="item-grid">
                    <!-- 36 slots (6x6) will be generated here -->
                </div>
            </div>
            
            <div class="vendor-panel">
                <h3>Vendor</h3>
                <div id="vendor-grid" class="item-grid">
                    <!-- 36 slots (6x6) will be generated here -->
                </div>
            </div>
        </div>
    </div>
    
    <script src="js/app.js"></script>
</body>
</html>
