local ESX = nil
local isInventoryOpen = false

-- Get ESX object
Citizen.CreateThread(function()
    while ESX == nil do
        TriggerEvent('esx:getSharedObject', function(obj) ESX = obj end)
        Citizen.Wait(0)
    end
end)

-- Disable weapon wheel
Citizen.CreateThread(function()
    while true do
        Citizen.Wait(0)
        DisableControlAction(0, 37, true) -- INPUT_SELECT_WEAPON
    end
end)

-- Register command to open inventory
RegisterCommand('inv', function()
    if not isInventoryOpen then
        OpenInventory()
    end
end, false)

-- Open inventory function
function OpenInventory()
    ESX.TriggerServerCallback('inventory_system:getData', function(data)
        if data then
            isInventoryOpen = true
            SetNuiFocus(true, true)
            SendNUIMessage({
                action = 'open',
                inventory = data.inventory,
                vendor = data.vendor,
                weight = data.weight,
                maxWeight = data.maxWeight
            })
        end
    end)
end

-- Close inventory function
function CloseInventory()
    isInventoryOpen = false
    SetNuiFocus(false, false)
    SendNUIMessage({
        action = 'close'
    })
end

-- NUI Callbacks
RegisterNUICallback('useItem', function(data, cb)
    ESX.TriggerServerCallback('inventory_system:useItem', function(success)
        cb({success = success})
    end, data.item)
end)

RegisterNUICallback('buyItem', function(data, cb)
    ESX.TriggerServerCallback('inventory_system:buyItem', function(success)
        cb({success = success})
        if success then
            -- Refresh inventory data
            ESX.TriggerServerCallback('inventory_system:getData', function(newData)
                if newData then
                    SendNUIMessage({
                        action = 'updateData',
                        inventory = newData.inventory,
                        vendor = newData.vendor,
                        weight = newData.weight,
                        maxWeight = newData.maxWeight
                    })
                end
            end)
        end
    end, data.item, data.quantity)
end)

RegisterNUICallback('sellItem', function(data, cb)
    ESX.TriggerServerCallback('inventory_system:sellItem', function(success)
        cb({success = success})
        if success then
            -- Refresh inventory data
            ESX.TriggerServerCallback('inventory_system:getData', function(newData)
                if newData then
                    SendNUIMessage({
                        action = 'updateData',
                        inventory = newData.inventory,
                        vendor = newData.vendor,
                        weight = newData.weight,
                        maxWeight = newData.maxWeight
                    })
                end
            end)
        end
    end, data.item, data.quantity)
end)

RegisterNUICallback('close', function(data, cb)
    CloseInventory()
    cb('ok')
end)

-- Close inventory on ESC
Citizen.CreateThread(function()
    while true do
        Citizen.Wait(0)
        if isInventoryOpen and IsControlJustPressed(0, 322) then -- ESC key
            CloseInventory()
        end
    end
end)
