let inventoryData = null;
let vendorData = null;

// Listen for messages from the game
window.addEventListener('message', function(event) {
    const data = event.data;
    
    switch(data.action) {
        case 'open':
            openInventory(data);
            break;
        case 'close':
            closeInventory();
            break;
        case 'updateData':
            updateInventoryData(data);
            break;
    }
});

function openInventory(data) {
    inventoryData = data.inventory;
    vendorData = data.vendor;
    
    updateWeightDisplay(data.weight, data.maxWeight);
    populateInventoryGrid();
    populateVendorGrid();
    
    document.getElementById('inventory-container').classList.remove('hidden');
}

function closeInventory() {
    document.getElementById('inventory-container').classList.add('hidden');
    
    // Post to close callback
    fetch(`https://${GetParentResourceName()}/close`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json; charset=UTF-8',
        },
        body: JSON.stringify({})
    });
}

function updateInventoryData(data) {
    inventoryData = data.inventory;
    vendorData = data.vendor;
    
    updateWeightDisplay(data.weight, data.maxWeight);
    populateInventoryGrid();
    populateVendorGrid();
}

function updateWeightDisplay(weight, maxWeight) {
    const weightText = document.getElementById('weight-text');
    const weightFill = document.getElementById('weight-fill');
    
    weightText.textContent = `${weight.toFixed(1)} / ${maxWeight.toFixed(1)} KG`;
    
    const percentage = (weight / maxWeight) * 100;
    weightFill.style.width = `${Math.min(percentage, 100)}%`;
}

function populateInventoryGrid() {
    const grid = document.getElementById('inventory-grid');
    grid.innerHTML = '';
    
    // Create 36 slots
    for (let i = 0; i < 36; i++) {
        const slot = document.createElement('div');
        slot.className = 'item-slot';
        
        // Find item for this slot
        const item = inventoryData && inventoryData[i];
        
        if (item && item.count > 0) {
            slot.innerHTML = `
                <img src="img/${item.name}.png" alt="${item.label}" class="item-icon" onerror="this.src='img/default.png'">
                <div class="item-count">${item.count}</div>
                <div class="item-name">${item.label}</div>
            `;
            
            slot.onclick = () => useItem(item);
        } else {
            slot.classList.add('empty-slot');
        }
        
        grid.appendChild(slot);
    }
}

function populateVendorGrid() {
    const grid = document.getElementById('vendor-grid');
    grid.innerHTML = '';
    
    // Create 36 slots
    for (let i = 0; i < 36; i++) {
        const slot = document.createElement('div');
        slot.className = 'item-slot';
        
        // Find vendor item for this slot
        const item = vendorData && vendorData[i];
        
        if (item) {
            slot.innerHTML = `
                <img src="img/${item.name}.png" alt="${item.label}" class="item-icon" onerror="this.src='img/default.png'">
                <div class="item-count">∞</div>
                <div class="item-name">${item.label}</div>
                <div class="item-price">$${item.price}</div>
            `;
            
            slot.onclick = () => buyItem(item);
            slot.oncontextmenu = (e) => {
                e.preventDefault();
                sellItem(item);
            };
        } else {
            slot.classList.add('empty-slot');
        }
        
        grid.appendChild(slot);
    }
}

function useItem(item) {
    fetch(`https://${GetParentResourceName()}/useItem`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json; charset=UTF-8',
        },
        body: JSON.stringify({
            item: item.name
        })
    })
    .then(resp => resp.json())
    .then(resp => {
        showFeedback(resp.success);
        if (resp.success) {
            // Item will be updated via updateData message from client
        }
    });
}

function buyItem(item) {
    fetch(`https://${GetParentResourceName()}/buyItem`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json; charset=UTF-8',
        },
        body: JSON.stringify({
            item: item.name,
            quantity: 1
        })
    })
    .then(resp => resp.json())
    .then(resp => {
        showFeedback(resp.success);
    });
}

function sellItem(item) {
    fetch(`https://${GetParentResourceName()}/sellItem`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json; charset=UTF-8',
        },
        body: JSON.stringify({
            item: item.name,
            quantity: 1
        })
    })
    .then(resp => resp.json())
    .then(resp => {
        showFeedback(resp.success);
    });
}

function showFeedback(success) {
    const container = document.getElementById('inventory-container');
    
    if (success) {
        container.style.borderColor = '#4CAF50';
        setTimeout(() => {
            container.style.borderColor = '#333';
        }, 500);
    } else {
        container.style.borderColor = '#F44336';
        setTimeout(() => {
            container.style.borderColor = '#333';
        }, 500);
    }
}

// Close button event
document.getElementById('close-btn').addEventListener('click', closeInventory);

// ESC key to close
document.addEventListener('keydown', function(event) {
    if (event.key === 'Escape') {
        closeInventory();
    }
});

// Prevent context menu
document.addEventListener('contextmenu', function(event) {
    event.preventDefault();
});
